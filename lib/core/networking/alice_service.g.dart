// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'alice_service.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$aliceHash() => r'146acb7fdea6d6a86c258bd7e3a2cc3d28ad8d7a';

/// Alice HTTP inspector service provider
/// Only initializes Alice in debug mode to avoid including it in production builds
///
/// Copied from [alice].
@ProviderFor(alice)
final aliceProvider = AutoDisposeProvider<Alice?>.internal(
  alice,
  name: r'aliceProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$aliceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AliceRef = AutoDisposeProviderRef<Alice?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
