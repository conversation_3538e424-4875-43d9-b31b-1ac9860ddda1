import 'package:alice/alice.dart';
import 'package:alice/model/alice_configuration.dart';
import 'package:alice_dio/alice_dio_adapter.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'alice_service.g.dart';

/// Alice HTTP inspector service provider
/// Only initializes Alice in debug mode to avoid including it in production builds
@riverpod
Alice? alice(Ref ref) {
  // Only create Alice instance in debug mode
  if (kDebugMode) {
    return Alice(
      configuration: AliceConfiguration(
        // Show notification overlay button for easy access
        showNotification: true,
        // Show inspector on shake gesture
        showInspectorOnShake: true,
      ),
    );
  }

  // Return null in release mode
  return null;
}

/// Extension to provide easy access to Alice instance
extension AliceServiceExtension on Ref {
  /// Get Alice instance if available (debug mode only)
  Alice? get alice => read(aliceProvider);
}
