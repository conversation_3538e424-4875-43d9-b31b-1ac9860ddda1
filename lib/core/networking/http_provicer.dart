import 'dart:io';

import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:kitemite_app/core/networking/alice_service.dart';
import 'package:kitemite_app/core/networking/api_interceptor.dart';
import 'package:kitemite_app/flavors.dart';
import 'package:kitemite_app/routing/router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:talker_dio_logger/talker_dio_logger.dart';
import 'package:talker_flutter/talker_flutter.dart';

part 'http_provicer.g.dart';

final talkerApp = TalkerFlutter.init();

@riverpod
Dio dio(Ref ref) {
  final dio = Dio(BaseOptions(
    baseUrl: F.baseURL,
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    validateStatus: (status) => true,
    receiveDataWhenStatusError: true,
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  ));

  // Add Alice interceptor first (only in debug mode)
  if (kDebugMode) {
    final alice = ref.read(aliceProvider);
    if (alice != null) {
      dio.interceptors.add(alice.getDioInterceptor());
    }
  }

  if (Platform.isAndroid) {
    dio.interceptors.add(
      TalkerDioLogger(
        talker: talkerApp,
        settings: const TalkerDioLoggerSettings(
          printRequestHeaders: true,
          printResponseMessage: true,
        ),
      ),
    );
  }
  dio.interceptors.add(CurlLoggerDioInterceptor(
    printOnSuccess: true,
  ));

  dio.interceptors.add(ApiInterceptor(ref));

  return dio;
}
