import 'package:awesome_notifications/awesome_notifications.dart' hide NotificationModel;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:kitemite_app/core/common/style/app_text_style.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/custom_app_bar.dart';
import 'package:kitemite_app/core/common/widgets/scaffold/scaffold_custom_widget.dart';
import 'package:kitemite_app/features/product_management/provider/product_management_provider.dart';
import 'package:kitemite_app/features/product_management/ui/product_management.dart';
import 'package:kitemite_app/gen/assets.gen.dart';
import 'package:kitemite_app/routing/router_paths.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../core/common/style/app_colors.dart';
import '../../../core/common/widgets/buttons/common_button.dart';
import '../../../model/response/notification/notification_model.dart';
import '../provider/notification_provider.dart';

class NotificationListScreen extends ConsumerStatefulWidget {
  const NotificationListScreen({super.key});

  @override
  ConsumerState<NotificationListScreen> createState() =>
      _NotificationListScreenState();
}

class _NotificationListScreenState
    extends ConsumerState<NotificationListScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(notificationNotifierProvider.notifier).fetchNotifications();
      AwesomeNotifications().resetGlobalBadge();
    });
  }

  Future<void> _refreshNotifications() async {
    await ref.read(notificationNotifierProvider.notifier).fetchNotifications();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(notificationNotifierProvider);
    final dateFormat = DateFormat('yyyy/MM/dd HH:mm');

    return BaseScaffold(
      appBar: CustomAppbar.basic(
        title: '通知',
        onTap: () {
          context.pop();
        },
      ),
      body: SafeArea(top: false, child: _buildBody(state)),
    );
  }

  Widget _buildBody(state) {
    if (state.isLoading) {
      return _buildSkeletonLoading();
    }

    if (state.error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                state.error ?? '',
                style:
                    AppTextStyles.regular(14.sp, color: AppColors.rambutan100),
              ),
              const SizedBox(height: 16),
              CommonButton(
                text: '再実行',
                onPressed: _refreshNotifications,
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshNotifications,
      child: state.notifications.isEmpty
          ? SizedBox(
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 100),
                  Assets.noEmail.image(),
                  Text(
                    'まだ通知はありません。',
                    style: AppTextStyles.regular(14.sp,
                        color: AppColors.textPrimary),
                  ),
                ],
              ))
          : NotificationListView(notifications: state.notifications),
    );
  }

  Widget _buildSkeletonLoading() {
    return Skeletonizer(
      enabled: true,
      effect: const ShimmerEffect(
        baseColor: AppColors.mono20,
        highlightColor: AppColors.mono40,
      ),
      child: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: 5,
        separatorBuilder: (context, index) => SizedBox(height: 8.h),
        itemBuilder: (context, index) {
          return _buildNotificationItemSkeleton();
        },
      ),
    );
  }

  Widget _buildNotificationItemSkeleton() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.mono20,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: const BoxDecoration(
                  color: AppColors.mono40,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Container(
                width: 60,
                height: 14,
                color: AppColors.mono40,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            height: 16,
            color: AppColors.mono40,
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            height: 16,
            color: AppColors.mono40,
          ),
          const SizedBox(height: 4),
          Container(
            width: 120,
            height: 12,
            color: AppColors.mono40,
          ),
        ],
      ),
    );
  }
}

class NotificationListView extends ConsumerWidget {
  final List<NotificationModel> notifications;

  const NotificationListView({
    super.key,
    required this.notifications,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: notifications.length,
      separatorBuilder: (context, index) => SizedBox(
        height: 8.h,
      ),
      itemBuilder: (context, index) {
        final notification = notifications[index];
        return GestureDetector(
          child: NotificationItem(
            notification: notification,
            onTap: () {
              if (!notification.isRead) {
                ref
                    .read(notificationNotifierProvider.notifier)
                    .markAsRead(notification.id ?? 0);
              }
              if (notification.isSoldHistories) {
                ref.read(productManagementNotifierProvider.notifier).refresh();
                context.go(RouterPaths.business,
                    extra: ProductManagementArg(
                      initialTabIndex: 2,
                      highlightProductId: notification.productId,
                      highlightOrderId: notification.orderId,
                    ));
                return;
              }
              context
                  .push(RouterPaths.notificationDetail, extra: notification.id)
                  .then((value) {
                ref
                    .read(notificationNotifierProvider.notifier)
                    .fetchNotifications();
              });
            },
          ),
        );
      },
    );
  }
}

class NotificationItem extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback onTap;

  const NotificationItem({
    super.key,
    required this.notification,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat('yyyy/MM/dd HH:mm');

    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color:
              notification.isRead ? const Color(0xFFF4F6F8) : AppColors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: notification.isRead
              ? null
              : [
                  const BoxShadow(
                    color: AppColors.mono20,
                    blurRadius: 3,
                    spreadRadius: 3,
                    offset: Offset(0, 2),
                  ),
                ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Assets.iconNotiPng.image(),
                const SizedBox(width: 8),
                Text(
                  "System",
                  style: AppTextStyles.regular(14.sp,
                      color: AppColors.textLightSecondary),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              notification.title ?? '',
              style: notification.isRead
                  ? AppTextStyles.regular(14.sp, color: AppColors.textPrimary)
                  : AppTextStyles.bold(14.sp, color: AppColors.textPrimary),
            ),
            const SizedBox(height: 4),
            Text(
              notification.message ?? '',
              style: AppTextStyles.regular(14.sp, color: AppColors.textPrimary),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              dateFormat.format(notification.createdAt?.toLocal() ??
                  DateTime.now().toLocal()),
              style: AppTextStyles.regular(12.sp, color: AppColors.mono60),
            ),
          ],
        ),
      ),
    );
  }
}
