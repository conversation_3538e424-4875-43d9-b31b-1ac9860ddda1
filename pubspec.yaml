name: kitemite_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev


version: 1.1.0+1

environment:
  sdk: ^3.5.3
dependencies:
  flutter:
    sdk: flutter
  hooks_riverpod: ^2.6.1
  flutter_hooks: ^0.20.5
  json_annotation: ^4.9.0
  riverpod_annotation: ^2.6.1
  shared_preferences: ^2.4.0
  go_router: ^14.8.0 
  intl: ^0.19.0
  dio: ^5.8.0
  retrofit: ^4.4.1
  pretty_dio_logger: ^1.4.0
  go_router_paths:
  talker_riverpod_logger: ^4.6.12
  google_fonts: ^6.2.1
  flutter_screenutil: ^5.9.3
  url_launcher: ^6.3.1
  flutter_localizations:
    sdk: flutter
  flutter_svg: ^2.0.17
  easy_rich_text: ^2.2.0
  sliding_up_panel: ^2.0.0+1
  freezed_annotation: ^2.4.4
  flutter_riverpod: ^2.6.1
  talker_dio_logger: ^4.6.14
  google_maps_flutter: ^2.10.1
  package_info_plus: ^8.3.0
  cached_network_image: ^3.4.1
  lottie:
  curl_logger_dio_interceptor: ^1.0.0
  image_picker: ^1.1.2
  geolocator: ^10.0.0
  flutter_slidable: ^3.1.2
  firebase_core: ^3.12.1
  firebase_messaging: ^15.2.4
  awesome_notifications: ^0.10.0
  device_info_plus: ^11.3.0
  flutter_secure_storage: ^9.2.4
  uuid: ^4.5.1
  multi_select_flutter: ^4.1.3
  currency_text_input_formatter: ^2.2.9
  permission_handler: ^11.3.0
  carousel_slider: ^5.0.0
  multi_image_picker_view: ^3.0.0
  webview_flutter: ^4.7.0
  textfield_tags: ^3.0.1
  flutter_blue_plus:
  equatable:
  flutter_emoji: ^2.5.1
  hive_ce: ^2.10.1
  hive_ce_flutter: ^1.1.0
  emoji_picker_flutter: ^4.3.0
  badges: ^3.1.2
  jwt_decoder: ^2.0.1
  flutter_native_splash: ^2.4.4
  open_settings_plus:
  skeletonizer: ^1.4.3
  flutter_custom_tabs: ^2.4.0
  talker_flutter: ^4.7.6
  vibration: ^3.1.3
  workmanager: ^0.7.0
  alice: ^1.0.0-dev.12
  flutter_alice: ^2.0.1
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.12
  json_serializable: ^6.8.0
  riverpod_lint: ^2.3.10
  riverpod_generator: ^2.4.0
  flutter_gen_runner: ^5.7.0
  retrofit_generator: ^9.0.0
  freezed: ^2.4.5
  intl_utils: ^2.8.2
  flutter_flavorizr: ^2.2.3
  flutter_launcher_icons: ^0.14.3
  hive_ce_generator: ^1.8.1


dependency_overrides:
 analyzer: 7.3.0
 analyzer_plugin: 0.12.0
 custom_lint_visitor: 1.0.0+7.3.0
 
 

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/
    - assets/animations/

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/app icon.png"
  min_sdk_android: 21 
 
flutter_native_splash:
  color: "#FFB92A" 
  image: assets/app_icon_2.png 
  android: true
  ios: true
  web: false

  android_12:
    image: assets/app_icon_2.png
    icon_background_color: "#FFB92A"
